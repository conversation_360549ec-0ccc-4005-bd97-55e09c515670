/**

# Variables

{
  "login": "gauntface",
  "reviewRequestsQueryString": "review-requested:gauntface is:open is:pr archived:false",
  "reviewedQueryString": "reviewed-by:gauntface is:open is:pr archived:false -author:gauntface",
  "mentionsQueryString": "reviewed-by:gauntface is:open is:pr archived:false -author:gauntface mentions:gauntface"
}

# Type queries here, and you will see intelligent autosuggestions
# aware of GitHub's current GraphQL schema, equipped with live
# syntax and validation errors highlighted within the text. We'll
# get you started with a simple query showing your username!

query IncomingPullRequests($login: String!, $reviewRequestsQueryString: String!, $reviewedQueryString: String!, $mentionsQueryString: String!) {
  reviewRequests: search(type: ISSUE, query: $reviewRequestsQueryString, last: 20) {
    nodes {
      ... on PullRequest {
        ...prFields
      }
    }
  }
  reviewed: search(type: ISSUE, query: $reviewedQueryString, last: 20) {
    nodes {
      ... on PullRequest {
        ...prFields
        ...lastCommentFields
        reviews(author: $login, last: 10) {
          nodes {
            ...reviewFields
            commit {
              oid
            }
          }
        }
        commits(last: 20) {
          nodes {
            commit {
              additions
              deletions
              changedFiles
              authoredDate
              pushedDate
              oid
            }
          }
        }
      }
    }
  }
  mentions: search(type: ISSUE, query: $mentionsQueryString, last: 20) {
    nodes {
      ...mentionedFields
    }
  }

  rateLimit {
    cost
    limit
    remaining
    resetAt
    nodeCount
  }
}

fragment prFields on PullRequest {
  repository {
    id
    name
    nameWithOwner
    owner {
      login
    }
  }
  title
  url
  number
  id
  mergeable
  createdAt
  viewerSubscription
  author {
    avatarUrl
    login
    url
  }
}

fragment reviewFields on PullRequestReview {
  submittedAt
  state
  author {
    login
  }
}

fragment lastCommentFields on PullRequest {
  comments(last: 1) {
    nodes {
      author {
        login
      }
      createdAt
    }
  }
}

fragment mentionedFields on PullRequest {
  id
  comments(last: 10) {
    nodes {
      createdAt
      bodyText
      url
    }
  }
  reviews(last: 10) {
    nodes {
      bodyText
      createdAt
      url
      comments(last: 10) {
        nodes {
          createdAt
          bodyText
          url
        }
      }
    }
  }
}

**/
