package main

import (
	"encoding/json"
	"errors"
	"os"
	"strings"
	"testing"

	"github.com/aws/aws-lambda-go/events"
	"github.com/google/go-cmp/cmp"
)

var reset func()

func TestMain(m *testing.M) {
	origEnvVars := os.Environ()
	reset = func() {
		os.Clearenv()
		for _, v := range origEnvVars {
			vs := strings.Split(v, "=")
			os.Setenv(vs[0], vs[1])
		}
	}
	m.Run()
}

func TestHandler(t *testing.T) {
	tests := []struct {
		description string
		env         map[string]string
		wantError   error
		want        events.APIGatewayProxyResponse
	}{
		{
			description: "return bare response for no enviroment vars",
			want: events.APIGatewayProxyResponse{
				Body: string(
					jsonMustMarshall(t, response{
						Status: "ok",
					}),
				),
				StatusCode: 200,
				Headers: map[string]string{
					"Content-Type": "application/json",
				},
			},
		},
		{
			description: "return full response with enviroment vars",
			env: map[string]string{
				"SIDECARS_ENV": "test",
			},
			want: events.APIGatewayProxyResponse{
				Body: string(
					jsonMustMarshall(t, response{
						Status:      "ok",
						Environment: "test",
					}),
				),
				StatusCode: 200,
				Headers: map[string]string{
					"Content-Type": "application/json",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.description, func(t *testing.T) {
			t.Cleanup(reset)

			os.Clearenv()
			for k, v := range tt.env {
				os.Setenv(k, v)
			}

			got, err := handler()
			if !errors.Is(err, tt.wantError) {
				t.Fatalf("Unexpected error; got %v, want %v", err, tt.wantError)
			}

			if diff := cmp.Diff(tt.want, got); diff != "" {
				t.Errorf("Unexpected result; diff %v", diff)
			}
		})
	}
}

func jsonMustMarshall(t *testing.T, v interface{}) []byte {
	t.Helper()

	g, err := json.Marshal(v)
	if err != nil {
		t.Fatalf("Failed to marshal value: %v", err)
	}

	return g
}
