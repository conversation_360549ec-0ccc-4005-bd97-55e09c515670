package main

import (
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/gauntface/sidecars-api/utils/jsonresponse"
)

func handler() (events.APIGatewayProxyResponse, error) {
	return jsonresponse.Data(response{
		Status:      "ok",
		Environment: os.Getenv("SIDECARS_ENV"),
	}), nil
}

func main() {
	lambda.Start(handler)
}

type response struct {
	Status      string `json:"status"`
	Environment string `json:"environment"`
}
