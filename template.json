{"AWSTemplateFormatVersion": "2010-09-09", "Transform": "AWS::Serverless-2016-10-31", "Description": "api.sidecars.dev\n", "Parameters": {"SidecarsEnv": {"Type": "String", "Description": "Which environment do you want to deploy to?", "AllowedValues": ["local", "dev", "staging", "prod"], "Default": "local"}, "GatewayCorsOrigins": {"Type": "String", "Description": "What origins are allowed to access this URL via the API Gateway", "Default": ""}, "LambdaCorsOrigins": {"Type": "String", "Description": "What origins are allowed to access this URL via lambda", "Default": ""}, "APIDomain": {"Type": "String", "Description": "The domain for the api service"}, "FirebaseProjectID": {"Type": "String", "Description": "The ID of the Firebase project used for auth"}}, "Globals": {"Function": {"Timeout": 5, "Environment": {"Variables": {"SIDECARS_ENV": {"Ref": "SidecarsEnv"}, "CORS_ALLOW_ORIGIN": {"Ref": "LambdaCorsOrigins"}}}}}, "Resources": {"ApiGatewayApi": {"Type": "AWS::Serverless::HttpApi", "Properties": {"StageName": "sidecars-api", "CorsConfiguration": {"AllowMethods": ["OPTIONS", "GET", "POST"], "AllowOrigins": {"Fn::Split": [",", {"Ref": "GatewayCorsOrigins"}]}, "AllowHeaders": ["Authorization"]}, "Auth": {"Authorizers": {"FirebaseAuthorizer": {"IdentitySource": "$request.header.Authorization", "JwtConfiguration": {"audience": [{"Ref": "FirebaseProjectID"}], "issuer": {"Fn::Sub": ["https://securetoken.google.com/${FirebaseProjectID}", {"FirebaseProjectID": {"Ref": "FirebaseProjectID"}}]}}}}, "DefaultAuthorizer": "FirebaseAuthorizer"}}, "Domain": {"DomainName": {"Ref": "APIDomain"}, "CertificateArn": {"Ref": "APIDomainARN"}, "EndpointConfiguration": "REGIONAL"}}, "BasePath": {"Type": "AWS::ApiGatewayV2::ApiMapping", "Properties": {"ApiId": {"Ref": "ApiGatewayApi"}, "DomainName": {"Ref": "APIDomain"}, "Stage": {"Ref": "ApiGatewayApi.Stage"}}, "DependsOn": ["ApiGatewayApi"]}, "HealthCheckFunction": {"Type": "AWS::Serverless::Function", "Metadata": {"BuildMethod": "makefile"}, "Properties": {"CodeUri": ".", "Handler": "bootstrap", "FunctionName": {"Fn::Sub": ["healthcheck_${SidecarsEnv}", {"SidecarsEnv": {"Ref": "SidecarsEnv"}}]}, "Runtime": "provided.al2", "Architectures": ["x86_64"], "Events": {"HTTPEvent": {"Type": "HttpApi", "Properties": {"ApiId": {"Ref": "ApiGatewayApi"}, "Path": "/ok", "Method": "GET", "Auth": {"Authorizer": "NONE"}}}}}}}}