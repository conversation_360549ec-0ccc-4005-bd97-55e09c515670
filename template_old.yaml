AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: focused-api

Parameters:
  SidecarsEnv:
    Type: String
    Default: local
    AllowedValues:
      - local
      - dev
      - staging
      - prod
  GitHubAppID:
    Type: String
  GitHubAppPrivateKey:
    Type: String
  GatewayCorsOrigins:
    Type: String
  FirebaseProjectID:
    Type: String
  APIDomain:
    Type: String
  CertificateARN:
    Type: String

Globals:
  Function:
    Timeout: 5
    Environment:
      Variables:
        SIDECARS_ENV: !Ref SidecarsEnv
        GITHUB_APP_ID: !Ref GitHubAppID
        GITHUB_APP_PRIVATE_KEY: !Ref GitHubAppPrivateKey

Resources:
  ApiGateway:
    Type: AWS::Serverless::HttpApi
    Properties:
      StageName: sidecars-api
      CorsConfiguration:
        AllowMethods:
          - OPTIONS
          - GET
          - POST
        AllowHeaders:
          - "Authorization"
        AllowOrigins: !Split [",", !Ref GatewayCorsOrigins]
      Auth:
        Authorizers:
          FirebaseAuthorizer:
            IdentitySource: $request.header.Authorization
            JwtConfiguration:
              audience:
                - !Ref FirebaseProjectID
              issuer: !Sub https://securetoken.google.com/${FirebaseProjectID}
        DefaultAuthorizer: FirebaseAuthorizer
      Domain:
        DomainName: !Ref APIDomain
        CertificateArn: !Ref CertificateARN
        EndpointConfiguration: REGIONAL


  HealthCheck:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: rust-cargolambda
      BuildProperties:
        Binary: healthcheck_ok
    Properties:
      CodeUri: ./lambda
      Handler: bootstrap
      Runtime: provided.al2
      Events:
        HTTPEvent:
          Type: "HttpApi"
          Properties:
            ApiId: !Ref ApiGateway
            Path: "/ok"
            Method: "GET"
            Auth:
              Authorizer: NONE

  GithubOutgoingPRs:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: rust-cargolambda
      BuildProperties:
        Binary: github_outgoing_prs
    Properties:
      CodeUri: ./lambda
      Handler: bootstrap
      Runtime: provided.al2
      Events:
        HTTPEvent:
          Type: "HttpApi"
          Properties:
            ApiId: !Ref ApiGateway
            Path: "/github/outgoing-prs"
            Method: "GET"
