on:
  workflow_dispatch:
  push:
    branches:
      - main
jobs:
  trigger-deploy:
    runs-on: ubuntu-latest
    steps:
      - run: >
          curl
          -X POST
          -H "Authorization: token ${{secrets.PAT_TOKEN}}"
          -H "Accept: application/vnd.github.v3+json"
          -H "Content-Type: application/json"
          https://api.github.com/repos/${{secrets.DEPLOY_REPO}}/dispatches
          -d '{"event_type": "api-push"}'
