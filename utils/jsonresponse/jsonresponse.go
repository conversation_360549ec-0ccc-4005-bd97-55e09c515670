package jsonresponse

import (
	"encoding/json"
	"net/http"
	"os"

	"github.com/aws/aws-lambda-go/events"
)

func Data(d interface{}, opts ...JSONOption) events.APIGatewayProxyResponse {
	os := jsonOption{
		StatusCode: http.StatusOK,
	}
	return proxyResponse(d, os, opts)
}

func Error(msg string, opts ...JSONOption) events.APIGatewayProxyResponse {
	os := jsonOption{
		StatusCode: http.StatusInternalServerError,
	}
	p := errorPayload{
		Error: errorData{
			Message: msg,
		},
	}
	return proxyResponse(p, os, opts)
}

func proxyResponse(payload interface{}, opts jsonOption, optFuncs []JSONOption) events.APIGatewayProxyResponse {
	for _, o := range optFuncs {
		o(&opts)
	}
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	if value, ok := os.LookupEnv("CORS_ALLOW_ORIGIN"); ok && len(value) > 0 {
		headers["Access-Control-Allow-Origin"] = value
	}
	return events.APIGatewayProxyResponse{
		Body:       jsonMustMarshal(payload),
		StatusCode: opts.StatusCode,
		Headers:    headers,
	}
}

func jsonMustMarshal(v interface{}) string {
	if v == nil {
		return ""
	}

	vb, _ := json.Marshal(v)
	return string(vb)
}

type JSONOption func(*jsonOption)

type jsonOption struct {
	StatusCode int
}

func WithStatusCode(s int) JSONOption {
	return func(o *jsonOption) {
		o.StatusCode = s
	}
}

type errorPayload struct {
	Error errorData `json:"error"`
}

type errorData struct {
	Message string `json:"message"`
}
