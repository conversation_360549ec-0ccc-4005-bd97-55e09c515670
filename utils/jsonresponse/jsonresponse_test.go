package jsonresponse

import (
	"net/http"
	"os"
	"strings"
	"testing"

	"github.com/aws/aws-lambda-go/events"
	"github.com/google/go-cmp/cmp"
)

var reset func()

func TestMain(m *testing.M) {
	origEnvVars := os.Environ()
	reset = func() {
		os.Clearenv()
		for _, v := range origEnvVars {
			vs := strings.Split(v, "=")
			os.Setenv(vs[0], vs[1])
		}
	}
	m.Run()
}

func TestData(t *testing.T) {
	tests := []struct {
		description string
		data        interface{}
		corsOrigin  string
		opts        []JSONOption
		want        events.APIGatewayProxyResponse
	}{
		{
			description: "return response without data",
			corsOrigin:  "http://example.local:1234",
			want: events.APIGatewayProxyResponse{
				StatusCode: http.StatusOK,
				Headers: map[string]string{
					"Content-Type":                "application/json",
					"Access-Control-Allow-Origin": "http://example.local:1234",
				},
			},
		},
		{
			description: "return response with data",
			corsOrigin:  "http://example.local:1234",
			data: struct {
				Hello string `json:"hello"`
				World int    `json:"number"`
				From  bool
				Test  []string
			}{
				Hello: "hi",
				World: 123,
				From:  true,
				Test:  []string{"this", "is", "a", "test"},
			},
			want: events.APIGatewayProxyResponse{
				Body:       `{"hello":"hi","number":123,"From":true,"Test":["this","is","a","test"]}`,
				StatusCode: http.StatusOK,
				Headers: map[string]string{
					"Content-Type":                "application/json",
					"Access-Control-Allow-Origin": "http://example.local:1234",
				},
			},
		},
		{
			description: "return response with options",
			corsOrigin:  "http://example.local:1234",
			data: struct {
				Hello string `json:"hello"`
			}{
				Hello: "world",
			},
			opts: []JSONOption{
				WithStatusCode(404),
			},
			want: events.APIGatewayProxyResponse{
				Body:       `{"hello":"world"}`,
				StatusCode: 404,
				Headers: map[string]string{
					"Content-Type":                "application/json",
					"Access-Control-Allow-Origin": "http://example.local:1234",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.description, func(t *testing.T) {
			defer reset()

			os.Setenv("CORS_ALLOW_ORIGIN", tt.corsOrigin)

			got := Data(tt.data, tt.opts...)

			if diff := cmp.Diff(tt.want, got); diff != "" {
				t.Errorf("Unexpected result; diff %v", diff)
			}
		})
	}
}

func TestError(t *testing.T) {
	tests := []struct {
		description string
		corsOrigin  string
		msg         string
		opts        []JSONOption
		want        events.APIGatewayProxyResponse
	}{
		{
			description: "return response without a message",
			corsOrigin:  "http://example.local:1234",
			want: events.APIGatewayProxyResponse{
				Body:       `{"error":{"message":""}}`,
				StatusCode: http.StatusInternalServerError,
				Headers: map[string]string{
					"Content-Type":                "application/json",
					"Access-Control-Allow-Origin": "http://example.local:1234",
				},
			},
		},
		{
			description: "return response with message",
			corsOrigin:  "http://example.local:1234",
			msg:         "example error",
			want: events.APIGatewayProxyResponse{
				Body:       `{"error":{"message":"example error"}}`,
				StatusCode: http.StatusInternalServerError,
				Headers: map[string]string{
					"Content-Type":                "application/json",
					"Access-Control-Allow-Origin": "http://example.local:1234",
				},
			},
		},
		{
			description: "return response with options",
			corsOrigin:  "http://example.local:1234",
			msg:         "example error",
			opts: []JSONOption{
				WithStatusCode(404),
			},
			want: events.APIGatewayProxyResponse{
				Body:       `{"error":{"message":"example error"}}`,
				StatusCode: 404,
				Headers: map[string]string{
					"Content-Type":                "application/json",
					"Access-Control-Allow-Origin": "http://example.local:1234",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.description, func(t *testing.T) {
			defer reset()

			os.Setenv("CORS_ALLOW_ORIGIN", tt.corsOrigin)

			got := Error(tt.msg, tt.opts...)

			if diff := cmp.Diff(tt.want, got); diff != "" {
				t.Errorf("Unexpected result; diff %v", diff)
			}
		})
	}
}
