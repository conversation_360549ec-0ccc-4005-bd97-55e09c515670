build-lambda-function:
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -tags lambda.norpc -o bootstrap lambda/$(DIR)/$(HANDLER).go
	mv bootstrap $(ARTIFACTS_DIR)/

build-HealthCheckFunction: HANDLER=healthcheck
build-HealthCheckFunction: DIR=healthcheck
build-HealthCheckFunction: build-lambda-function

# build-GetPrioritiesForDayFunction: HANDLER=prioritiesfordayget
# build-GetPrioritiesForDayFunction: DIR=priorities/prioritiesfordayget
# build-GetPrioritiesForDayFunction: build-lambda-function

# build-PostPrioritiesForDayFunction: HANDLER=prioritiesfordaypost
# build-PostPrioritiesForDayFunction: DIR=priorities/prioritiesfordaypost
# build-PostPrioritiesForDayFunction: build-lambda-function

# build-GetNotesForDayFunction: HANDLER=notesfordayget
# build-GetNotesForDayFunction: DIR=notes/notesfordayget
# build-GetNotesForDayFunction: build-lambda-function

# build-PostNotesForDayFunction: HANDLER=notesfordaypost
# build-PostNotesForDayFunction: DIR=notes/notesfordaypost
# build-PostNotesForDayFunction: build-lambda-function

# build-GetPrioritiesForDatesFunction: HANDLER=prioritiesfordatesget
# build-GetPrioritiesForDatesFunction: DIR=priorities/prioritiesfordatesget
# build-GetPrioritiesForDatesFunction: build-lambda-function
