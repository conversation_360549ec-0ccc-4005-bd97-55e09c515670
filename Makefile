GIT_HASH=$$(git rev-parse --short HEAD)

include lambda.mk

clean:
	rm -rf .aws-sam \
	go mod tidy

build: clean
	sam build

test:
	go test ./...

test-ci:
	go test -race -coverprofile=coverage.out -covermode=atomic ./...

test-coverage: test-ci
	go tool cover -html=coverage.out

localserver: build
	sam local start-api \
		--port 3001 \
		--force-image-build \
		--config-env=local \
		--config-file="samconfig.toml" \
		--parameter-overrides="FocusEnv=local GatewayCorsOrigins=http://localhost:3000 LambdaCorsOrigins=\"*\" DynamodbEndpoint=http://dynamodb:8000 Version=${GIT_HASH}"
# 		--docker-network focus-api_local-api-network

server:
	make localserver
# 	make -j3 localserver localdynamodb localdynamodbadmin

deploy-dev: build
	sam deploy \
		--config-env=dev \
		--config-file="samconfig.toml" \
		--no-fail-on-empty-changeset

deploy-dev-database:
	sam deploy \
		--template-file="dbs/priorities.yaml" \
		--config-env=dev \
		--config-file="../samconfig.toml" \
		--no-fail-on-empty-changeset || true
	sam deploy \
		--template-file="dbs/notes.yaml" \
		--config-env=dev \
		--config-file="../samconfig.toml" \
		--no-fail-on-empty-changeset || true
